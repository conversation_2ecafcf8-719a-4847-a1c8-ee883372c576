<?php

use PHPUnit\Framework\TestCase;
use DynastyTradeCalculator\Membership;

/**
 * Test class for custom calculator-only proration logic
 * Tests that upgrades use calculator pricing only, excluding RotoGPT components
 */
class CustomProrationTest extends TestCase
{
    private $membership;

    protected function setUp(): void
    {
        $this->membership = new Membership();
    }

    /**
     * Test calculator pricing constants are correctly defined
     */
    public function testCalculatorPricingConstants()
    {
        $reflection = new ReflectionClass(Membership::class);
        
        $this->assertEquals(2.99, $reflection->getConstant('CALCULATOR_MONTHLY_PRICE'));
        $this->assertEquals(29.99, $reflection->getConstant('CALCULATOR_YEARLY_PRICE'));
        $this->assertEquals(0.099, $reflection->getConstant('CALCULATOR_DAILY_RATE_MONTHLY'));
        $this->assertEquals(0.082, $reflection->getConstant('CALCULATOR_DAILY_RATE_YEARLY'));
    }

    /**
     * Test getCalculatorPricing method for monthly memberships
     */
    public function testGetCalculatorPricingMonthly()
    {
        $mockMembership = $this->createMockMembership(7, 'month'); // $4.99 tier
        
        $method = $this->getPrivateMethod('getCalculatorPricing');
        $result = $method->invoke($this->membership, $mockMembership);
        
        $this->assertIsArray($result);
        $this->assertEquals(2.99, $result['price']);
        $this->assertEquals(0.099, $result['daily_rate']);
    }

    /**
     * Test getCalculatorPricing method for yearly memberships
     */
    public function testGetCalculatorPricingYearly()
    {
        $mockMembership = $this->createMockMembership(11, 'year'); // $29.99 yearly tier
        
        $method = $this->getPrivateMethod('getCalculatorPricing');
        $result = $method->invoke($this->membership, $mockMembership);
        
        $this->assertIsArray($result);
        $this->assertEquals(29.99, $result['price']);
        $this->assertEquals(0.082, $result['daily_rate']);
    }

    /**
     * Test getCalculatorPricing returns null for non-calculator memberships
     */
    public function testGetCalculatorPricingNonCalculator()
    {
        $mockMembership = $this->createMockMembership(999, 'day'); // Non-standard duration
        
        $method = $this->getPrivateMethod('getCalculatorPricing');
        $result = $method->invoke($this->membership, $mockMembership);
        
        $this->assertNull($result);
    }

    /**
     * Test custom proration calculation for monthly membership upgrade on day 1
     * Should return $2.89 credit (full $2.99 minus 1 day at $0.099/day)
     */
    public function testCalculatorProrationMonthlyDay1()
    {
        $mockOldMembership = $this->createMockMembershipWithExpiration(7, 'month', 29); // 29 days remaining
        
        $method = $this->getPrivateMethod('calculateCalculatorProration');
        $result = $method->invoke($this->membership, $mockOldMembership);
        
        // Expected: $2.99 * (29/30) = $2.89
        $expected = round(2.99 * (29/30), 2);
        $this->assertEquals($expected, $result);
        $this->assertGreaterThan(2.80, $result);
        $this->assertLessThan(2.99, $result);
    }

    /**
     * Test custom proration calculation for yearly membership upgrade
     */
    public function testCalculatorProrationYearlyMidYear()
    {
        $mockOldMembership = $this->createMockMembershipWithExpiration(11, 'year', 182); // ~6 months remaining
        
        $method = $this->getPrivateMethod('calculateCalculatorProration');
        $result = $method->invoke($this->membership, $mockOldMembership);
        
        // Expected: $29.99 * (182/365) ≈ $14.95
        $expected = round(29.99 * (182/365), 2);
        $this->assertEquals($expected, $result);
        $this->assertGreaterThan(14.00, $result);
        $this->assertLessThan(16.00, $result);
    }

    /**
     * Test that downgrades return 0 proration (existing behavior)
     */
    public function testDowngradeProrationDisabled()
    {
        $mockMembership = $this->createMockUpgradeMembership(12, 9); // $9.99 to $6.99 downgrade
        
        $result = $this->membership->disableProrationForDowngrades(4.99, 123, $mockMembership);
        
        $this->assertEquals(0, $result);
    }

    /**
     * Test that upgrades use custom calculator proration instead of RCP default
     */
    public function testUpgradeUsesCalculatorProration()
    {
        $mockMembership = $this->createMockUpgradeMembership(7, 9); // $4.99 to $6.99 upgrade
        $originalRcpDiscount = 4.99; // What RCP would normally calculate
        
        $result = $this->membership->disableProrationForDowngrades($originalRcpDiscount, 123, $mockMembership);
        
        // Should return calculator-only proration, not the original RCP discount
        $this->assertNotEquals($originalRcpDiscount, $result);
        $this->assertGreaterThan(0, $result);
        $this->assertLessThan(2.99, $result); // Should be less than full calculator price
    }

    /**
     * Test proration calculation with expired membership returns 0
     */
    public function testExpiredMembershipProration()
    {
        $mockOldMembership = $this->createMockMembershipWithExpiration(7, 'month', -5); // Expired 5 days ago
        
        $method = $this->getPrivateMethod('calculateCalculatorProration');
        $result = $method->invoke($this->membership, $mockOldMembership);
        
        $this->assertEquals(0, $result);
    }

    /**
     * Helper method to access private methods for testing
     */
    private function getPrivateMethod($methodName)
    {
        $reflection = new ReflectionClass(Membership::class);
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        return $method;
    }

    /**
     * Create a mock membership with specific level and duration unit
     */
    private function createMockMembership($levelId, $durationUnit)
    {
        $mockMembership = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['get_object_id'])
            ->getMock();
        $mockMembership->method('get_object_id')->willReturn($levelId);

        // Mock the membership level
        $mockLevel = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['get_duration_unit', 'get_duration'])
            ->getMock();
        $mockLevel->method('get_duration_unit')->willReturn($durationUnit);
        $mockLevel->method('get_duration')->willReturn(1);

        // Mock the global function
        global $mock_rcp_get_membership_level;
        $mock_rcp_get_membership_level = $mockLevel;

        return $mockMembership;
    }

    /**
     * Create a mock membership with expiration date for proration testing
     */
    private function createMockMembershipWithExpiration($levelId, $durationUnit, $daysRemaining)
    {
        $mockMembership = $this->createMockMembership($levelId, $durationUnit);
        
        // Add expiration date method
        $mockMembership = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['get_object_id', 'get_expiration_date'])
            ->getMock();
        $mockMembership->method('get_object_id')->willReturn($levelId);
        
        $expirationDate = date('Y-m-d H:i:s', strtotime("+{$daysRemaining} days"));
        $mockMembership->method('get_expiration_date')->willReturn($expirationDate);

        return $mockMembership;
    }

    /**
     * Create a mock membership that represents an upgrade/downgrade scenario
     */
    private function createMockUpgradeMembership($oldLevelId, $newLevelId)
    {
        $mockMembership = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['was_upgrade', 'get_upgraded_from', 'get_object_id'])
            ->getMock();
        
        $mockMembership->method('was_upgrade')->willReturn(true);
        $mockMembership->method('get_upgraded_from')->willReturn(999); // Mock old membership ID
        $mockMembership->method('get_object_id')->willReturn($newLevelId);

        // Mock the old membership
        $mockOldMembership = $this->createMockMembershipWithExpiration($oldLevelId, 'month', 29);
        
        // Mock the global function for getting old membership
        global $mock_rcp_get_membership;
        $mock_rcp_get_membership = $mockOldMembership;

        return $mockMembership;
    }
}

// Mock global functions for testing
if (!function_exists('rcp_get_membership_level')) {
    function rcp_get_membership_level($levelId) {
        global $mock_rcp_get_membership_level;
        return $mock_rcp_get_membership_level ?? null;
    }
}

if (!function_exists('rcp_get_membership')) {
    function rcp_get_membership($membershipId) {
        global $mock_rcp_get_membership;
        return $mock_rcp_get_membership ?? null;
    }
}

if (!function_exists('current_time')) {
    function current_time($type) {
        return time();
    }
}
